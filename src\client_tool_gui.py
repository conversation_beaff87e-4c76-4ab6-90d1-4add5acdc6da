﻿# Simple GUI entry point for MTYB Steam Manifest Tool
# Features clean license entry interface with hidden advanced features

import customtkinter as ctk
from gui.simple_main_window import main_gui

# Configure customtkinter for modern appearance
ctk.set_appearance_mode("dark")  # Dark theme for better aesthetics
ctk.set_default_color_theme("blue")  # Modern blue theme

if __name__ == '__main__':
    print("🎮 Starting MTYB Steam Manifest Tool - Simple Mode")
    print("💡 Features:")
    print("   • Clean license key entry interface")
    print("   • Beautiful animations and modern design")
    print("   • Hidden advanced features (Ctrl+Shift+A)")
    print("   • Simplified user experience")
    print("   • Focus on license authentication")
    print()

    main_gui()

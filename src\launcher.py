#!/usr/bin/env python3
"""
MTYB Steam Tool Launcher

This script allows users to choose between different GUI modes:
1. Simple Mode - Clean license entry interface (recommended)
2. Advanced Mode - Full featured interface with all options visible
3. Modern Mode - Dashboard-style interface with hidden admin panel
"""

import sys
import os
import customtkinter as ctk
from tkinter import messagebox

# Add the src directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class LauncherGUI:
    def __init__(self):
        # Configure CustomTkinter
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        self.root = ctk.CTk()
        self.root.title("MTYB Steam Tool - Launcher")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        self.setup_ui()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_ui(self):
        """Setup the launcher UI"""
        # Main container
        main_frame = ctk.CTkFrame(self.root, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="🎮 MTYB Steam Tool",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(
            main_frame,
            text="Choose your preferred interface",
            font=ctk.CTkFont(size=16),
            text_color=["#6b7280", "#9ca3af"]
        )
        subtitle_label.pack(pady=(0, 30))
        
        # Mode selection buttons
        self.create_mode_buttons(main_frame)
        
        # Footer
        footer_label = ctk.CTkLabel(
            main_frame,
            text="💡 Tip: You can always switch modes later",
            font=ctk.CTkFont(size=12),
            text_color=["#9ca3af", "#6b7280"]
        )
        footer_label.pack(side="bottom", pady=10)
    
    def create_mode_buttons(self, parent):
        """Create mode selection buttons"""
        # Simple Mode (Recommended)
        simple_frame = ctk.CTkFrame(parent, corner_radius=15)
        simple_frame.pack(fill="x", pady=10)
        
        simple_title = ctk.CTkLabel(
            simple_frame,
            text="🌟 Simple Mode (Recommended)",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=["#10b981", "#34d399"]
        )
        simple_title.pack(pady=(15, 5))
        
        simple_desc = ctk.CTkLabel(
            simple_frame,
            text="Clean license entry interface with beautiful animations.\nAdvanced features hidden behind Ctrl+Shift+A.",
            font=ctk.CTkFont(size=12),
            text_color=["#6b7280", "#9ca3af"]
        )
        simple_desc.pack(pady=(0, 10))
        
        simple_btn = ctk.CTkButton(
            simple_frame,
            text="🚀 Launch Simple Mode",
            width=200,
            height=40,
            command=self.launch_simple_mode,
            fg_color=["#10b981", "#059669"],
            hover_color=["#059669", "#047857"]
        )
        simple_btn.pack(pady=(0, 15))
        
        # Modern Mode
        modern_frame = ctk.CTkFrame(parent, corner_radius=15)
        modern_frame.pack(fill="x", pady=10)
        
        modern_title = ctk.CTkLabel(
            modern_frame,
            text="🎨 Modern Mode",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        modern_title.pack(pady=(15, 5))
        
        modern_desc = ctk.CTkLabel(
            modern_frame,
            text="Dashboard-style interface with cards and navigation.\nAdmin panel accessible via Ctrl+Shift+A.",
            font=ctk.CTkFont(size=12),
            text_color=["#6b7280", "#9ca3af"]
        )
        modern_desc.pack(pady=(0, 10))
        
        modern_btn = ctk.CTkButton(
            modern_frame,
            text="🎨 Launch Modern Mode",
            width=200,
            height=40,
            command=self.launch_modern_mode
        )
        modern_btn.pack(pady=(0, 15))
        
        # Advanced Mode
        advanced_frame = ctk.CTkFrame(parent, corner_radius=15)
        advanced_frame.pack(fill="x", pady=10)
        
        advanced_title = ctk.CTkLabel(
            advanced_frame,
            text="🔧 Advanced Mode",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        advanced_title.pack(pady=(15, 5))
        
        advanced_desc = ctk.CTkLabel(
            advanced_frame,
            text="Full-featured interface with all options visible.\nFor power users and advanced configuration.",
            font=ctk.CTkFont(size=12),
            text_color=["#6b7280", "#9ca3af"]
        )
        advanced_desc.pack(pady=(0, 10))
        
        advanced_btn = ctk.CTkButton(
            advanced_frame,
            text="🔧 Launch Advanced Mode",
            width=200,
            height=40,
            command=self.launch_advanced_mode,
            fg_color=["#f59e0b", "#d97706"],
            hover_color=["#d97706", "#b45309"]
        )
        advanced_btn.pack(pady=(0, 15))
    
    def launch_simple_mode(self):
        """Launch the simple mode interface"""
        try:
            self.root.destroy()
            from gui.simple_main_window import main_gui
            main_gui()
        except ImportError as e:
            messagebox.showerror("Error", f"Failed to launch Simple Mode: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Unexpected error: {e}")
    
    def launch_modern_mode(self):
        """Launch the modern mode interface"""
        try:
            self.root.destroy()
            from gui.modern_main_window import main_gui
            main_gui()
        except ImportError as e:
            messagebox.showerror("Error", f"Failed to launch Modern Mode: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Unexpected error: {e}")
    
    def launch_advanced_mode(self):
        """Launch the advanced mode interface"""
        try:
            self.root.destroy()
            from gui.main_window import main_gui
            main_gui()
        except ImportError as e:
            messagebox.showerror("Error", f"Failed to launch Advanced Mode: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Unexpected error: {e}")
    
    def run(self):
        """Start the launcher"""
        self.root.mainloop()


def main():
    """Main entry point"""
    try:
        launcher = LauncherGUI()
        launcher.run()
    except Exception as e:
        print(f"Error starting launcher: {e}")
        # Fallback to simple mode
        try:
            from gui.simple_main_window import main_gui
            main_gui()
        except:
            print("Failed to start any GUI mode. Please check your installation.")
            sys.exit(1)


if __name__ == '__main__':
    main()

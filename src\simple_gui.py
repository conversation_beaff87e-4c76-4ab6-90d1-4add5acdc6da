#!/usr/bin/env python3
"""
Simple GUI Entry Point for MTYB Steam Tool

This script launches the simplified GUI version that shows only the license key entry
interface by default, with advanced features hidden behind Ctrl+Shift+A.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gui.simple_main_window import main_gui
    
    if __name__ == '__main__':
        print("Starting MTYB Steam Tool - Simple GUI...")
        main_gui()
        
except ImportError as e:
    print(f"Error importing GUI modules: {e}")
    print("Please ensure all dependencies are installed:")
    print("pip install customtkinter")
    sys.exit(1)
except Exception as e:
    print(f"Error starting GUI: {e}")
    sys.exit(1)

@echo off
title MTYB Steam Tool - GUI Selector
echo Starting MTYB Steam Tool - GUI Mode Selector...
echo.

cd /d "%~dp0"
python src/launcher.py

if %errorlevel% neq 0 (
    echo.
    echo Error: Failed to start the launcher.
    echo Falling back to Simple GUI mode...
    echo.
    python src/client_tool_gui.py
    
    if %errorlevel% neq 0 (
        echo.
        echo Error: Failed to start any GUI mode.
        echo Please ensure Python and required dependencies are installed.
        echo.
        pause
    )
)

import customtkinter as ctk
import threading
import webbrowser
from datetime import datetime
from tkinter import messagebox, simpledialog
import tkinter as tk
import time

from .config.app_settings import APP_NAME, STEAM_PATH, DATABASE_FILE, KEY_FILE, LOCK_FILE
from .config.ui_constants import UI_TEXT, UI_DIMENSIONS, UI_COLORS
from .components.theme_manager import ThemeManager
from .components.system_tray import SystemTray
from .views.dashboard_view import DashboardView
from .views.admin_view import AdminView

from gui_backend.encryption import initialize_encryption
from gui_backend.database import checkDatabase, read_all_license, read_license, read_all_app_id
from gui_backend.auth_service import initialize_keyauth, authenticate_license
from gui_backend.manifest_api import process_manifest, update_manifest, get_manifest
from gui_backend.steam_operations import remove_lock, clearCache
from gui_backend.system_tools import addDefenderExclusion, checkKoaLoader, checkGreenLuma, checkMiniTool


class SimpleSteamManifestGUI:
    def __init__(self):
        # Initialize CustomTkinter with modern theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        self.root = ctk.CTk()
        self.root.title("MTYB Steam Tool")
        self.root.geometry("600x500")
        self.root.minsize(500, 400)
        self.root.resizable(True, True)
        
        # Center the window
        self.center_window()
        
        # Application state
        self.authenticated = False
        self.current_license = None
        self.keyauthapp = None
        self.server_url = None
        self.encryption_key = None
        self.advanced_mode = False
        self.steam_path = STEAM_PATH
        
        # Animation variables
        self.animation_running = False
        self.pulse_direction = 1
        self.pulse_alpha = 0.5
        
        # Initialize backend services
        self._initialize_backend()
        
        # Setup simple UI (license entry only)
        self.setup_simple_ui()
        
        # Bind keyboard shortcuts
        self.setup_keyboard_shortcuts()
        
        # Initialize app
        self.initialize_app()
        
        # Start animations
        self.start_animations()
        
        # Setup system tray
        self.system_tray = SystemTray(self)
        try:
            self.system_tray.setup_system_tray()
        except:
            pass
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def _initialize_backend(self):
        """Initialize backend services"""
        try:
            # Initialize encryption
            self.encryption_key = initialize_encryption(KEY_FILE)
            
            # Initialize KeyAuth
            self.keyauthapp = initialize_keyauth()
            self.server_url = self.keyauthapp.var("SERVER_API") or "https://manifest.online-mtyb.com"
            
        except Exception as e:
            self.log_message(f"[MTYB] Backend initialization error: {str(e)}")
    
    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for advanced access"""
        self.root.bind('<Control-Shift-A>', self.toggle_advanced_mode)
        self.root.bind('<Escape>', self.close_advanced_mode)
        self.root.bind('<Return>', lambda e: self.authenticate_license())
    
    def setup_simple_ui(self):
        """Setup the simple license entry UI"""
        # Main container with gradient-like background
        self.main_container = ctk.CTkFrame(
            self.root, 
            fg_color=["#f0f0f0", "#1a1a1a"],
            corner_radius=0
        )
        self.main_container.pack(fill="both", expand=True)
        
        # Create the license entry interface
        self.create_license_interface()
    
    def create_license_interface(self):
        """Create the main license entry interface with animations"""
        # Clear existing widgets
        for widget in self.main_container.winfo_children():
            widget.destroy()
        
        # Center frame for license entry
        center_frame = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        center_frame.pack(expand=True, fill="both")
        
        # Logo/Title area with animation
        self.logo_frame = ctk.CTkFrame(
            center_frame,
            fg_color="transparent",
            height=150
        )
        self.logo_frame.pack(pady=(50, 30))
        self.logo_frame.pack_propagate(False)
        
        # Animated title
        self.title_label = ctk.CTkLabel(
            self.logo_frame,
            text="🎮 MTYB Steam Tool",
            font=ctk.CTkFont(size=32, weight="bold"),
            text_color=["#2563eb", "#3b82f6"]
        )
        self.title_label.pack(pady=20)
        
        # Animated subtitle
        self.subtitle_label = ctk.CTkLabel(
            self.logo_frame,
            text="Enter your license key to continue",
            font=ctk.CTkFont(size=16),
            text_color=["#6b7280", "#9ca3af"]
        )
        self.subtitle_label.pack()
        
        # License entry section
        entry_frame = ctk.CTkFrame(
            center_frame,
            corner_radius=20,
            fg_color=["#ffffff", "#2d2d2d"],
            border_width=2,
            border_color=["#e5e7eb", "#374151"]
        )
        entry_frame.pack(pady=20, padx=50, fill="x")
        
        # License key label
        license_label = ctk.CTkLabel(
            entry_frame,
            text="🔑 License Key",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=["#374151", "#f3f4f6"]
        )
        license_label.pack(pady=(30, 10))
        
        # License entry with modern styling
        self.license_entry = ctk.CTkEntry(
            entry_frame,
            placeholder_text="Enter your license key here...",
            width=400,
            height=50,
            font=ctk.CTkFont(size=14),
            corner_radius=15,
            border_width=2,
            border_color=["#d1d5db", "#4b5563"]
        )
        self.license_entry.pack(pady=10)
        
        # Authenticate button with hover animation
        self.auth_button = ctk.CTkButton(
            entry_frame,
            text="🚀 Authenticate",
            width=200,
            height=45,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=15,
            command=self.authenticate_license,
            fg_color=["#2563eb", "#3b82f6"],
            hover_color=["#1d4ed8", "#2563eb"]
        )
        self.auth_button.pack(pady=(10, 30))
        
        # Status area
        self.status_frame = ctk.CTkFrame(
            center_frame,
            fg_color="transparent",
            height=100
        )
        self.status_frame.pack(fill="x", pady=20)
        self.status_frame.pack_propagate(False)
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready to authenticate",
            font=ctk.CTkFont(size=14),
            text_color=["#6b7280", "#9ca3af"]
        )
        self.status_label.pack(pady=10)
        
        # Hidden hint for advanced mode
        hint_label = ctk.CTkLabel(
            center_frame,
            text="Press Ctrl+Shift+A for advanced features",
            font=ctk.CTkFont(size=10),
            text_color=["#d1d5db", "#374151"]
        )
        hint_label.pack(side="bottom", pady=10)
    
    def start_animations(self):
        """Start UI animations"""
        self.animation_running = True
        self.animate_pulse()
    
    def animate_pulse(self):
        """Create a subtle pulsing animation for the title"""
        if not self.animation_running:
            return
        
        try:
            # Update pulse alpha
            self.pulse_alpha += 0.02 * self.pulse_direction
            if self.pulse_alpha >= 1.0:
                self.pulse_alpha = 1.0
                self.pulse_direction = -1
            elif self.pulse_alpha <= 0.7:
                self.pulse_alpha = 0.7
                self.pulse_direction = 1
            
            # Apply subtle color change to title
            if hasattr(self, 'title_label'):
                alpha_hex = format(int(self.pulse_alpha * 255), '02x')
                color = f"#3b82f6{alpha_hex}" if ctk.get_appearance_mode() == "Dark" else f"#2563eb{alpha_hex}"
                # Note: CustomTkinter doesn't support alpha in hex, so we'll use a simpler approach
                
            # Schedule next animation frame
            self.root.after(50, self.animate_pulse)
        except:
            pass
    
    def authenticate_license(self):
        """Authenticate the entered license key"""
        license_key = self.license_entry.get().strip()
        if not license_key:
            self.update_status("❌ Please enter a license key", "error")
            return
        
        self.auth_button.configure(state="disabled", text="🔄 Authenticating...")
        self.update_status("🔄 Checking license...", "info")
        
        # Run authentication in background thread
        threading.Thread(
            target=self._authenticate_background, 
            args=(license_key,), 
            daemon=True
        ).start()
    
    def _authenticate_background(self, license_key):
        """Background authentication process"""
        try:
            self.log_message(f"[MTYB] Authenticating license: {license_key[:8]}...")
            
            authenticated, app_info = authenticate_license(
                license_key, self.keyauthapp, DATABASE_FILE, self.encryption_key
            )
            
            if authenticated and app_info:
                self.authenticated = True
                self.current_license = app_info
                self.log_message("[MTYB] Authentication successful!")
                
                # Update UI on main thread
                self.root.after(0, self._auth_success)
                
                # Run post-auth setup
                self._post_auth_setup()
            else:
                self.log_message("[MTYB] Authentication failed.")
                self.root.after(0, self._auth_failed)
                
        except Exception as e:
            self.log_message(f"[MTYB] Authentication error: {str(e)}")
            self.root.after(0, self._auth_failed)
    
    def _auth_success(self):
        """Update UI after successful authentication"""
        self.auth_button.configure(state="normal", text="✅ Authenticated")
        self.update_status("✅ Authentication successful! Ready to use.", "success")
        
        # Show success animation
        self.show_success_animation()
    
    def _auth_failed(self):
        """Update UI after failed authentication"""
        self.auth_button.configure(state="normal", text="🚀 Authenticate")
        self.update_status("❌ Authentication failed. Please check your license key.", "error")
    
    def show_success_animation(self):
        """Show success animation after authentication"""
        # Create a temporary success overlay
        success_frame = ctk.CTkFrame(
            self.main_container,
            fg_color=["#10b981", "#059669"],
            corner_radius=20
        )
        success_frame.place(relx=0.5, rely=0.5, anchor="center", relwidth=0.8, relheight=0.3)
        
        success_label = ctk.CTkLabel(
            success_frame,
            text="🎉 Welcome! Authentication Successful!",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="white"
        )
        success_label.pack(expand=True)
        
        # Remove after 2 seconds
        self.root.after(2000, success_frame.destroy)
    
    def update_status(self, message, status_type="info"):
        """Update status message with color coding"""
        colors = {
            "info": ["#3b82f6", "#60a5fa"],
            "success": ["#10b981", "#34d399"],
            "error": ["#ef4444", "#f87171"],
            "warning": ["#f59e0b", "#fbbf24"]
        }
        
        color = colors.get(status_type, colors["info"])
        self.status_label.configure(text=message, text_color=color)
    
    def toggle_advanced_mode(self, event=None):
        """Toggle advanced mode with password prompt"""
        if not self.advanced_mode:
            password = simpledialog.askstring(
                "Advanced Access",
                "Enter password for advanced features:",
                show='*'
            )
            
            # Simple password check (change this to a secure password)
            if password == "admin123":
                self.advanced_mode = True
                self.show_advanced_interface()
                messagebox.showinfo("Success", "Advanced mode activated!")
            else:
                messagebox.showerror("Error", "Invalid password!")
        else:
            self.advanced_mode = False
            self.create_license_interface()
    
    def show_advanced_interface(self):
        """Show the full advanced interface"""
        # Clear current interface
        for widget in self.main_container.winfo_children():
            widget.destroy()
        
        # Resize window for advanced mode
        self.root.geometry("1000x700")
        self.center_window()
        
        # Create advanced interface similar to modern_main_window
        self.setup_advanced_ui()
    
    def setup_advanced_ui(self):
        """Setup the advanced UI with full navigation"""
        # Header
        header_frame = ctk.CTkFrame(self.main_container, height=80, corner_radius=15)
        header_frame.pack(fill="x", pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text="🔧 MTYB Steam Tool - Advanced Mode",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=25)
        
        # Exit advanced mode button
        exit_btn = ctk.CTkButton(
            header_frame,
            text="🔒 Exit Advanced Mode",
            width=150,
            command=self.close_advanced_mode,
            fg_color=["#ef4444", "#dc2626"]
        )
        exit_btn.pack(side="right", padx=20, pady=25)
        
        # Main content area
        content_container = ctk.CTkFrame(self.main_container)
        content_container.pack(fill="both", expand=True)
        
        # Navigation sidebar
        nav_frame = ctk.CTkFrame(content_container, width=200, corner_radius=15)
        nav_frame.pack(side="left", fill="y", padx=(0, 10))
        nav_frame.pack_propagate(False)
        
        # Navigation title
        nav_title = ctk.CTkLabel(
            nav_frame,
            text="Navigation",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        nav_title.pack(pady=(20, 10))
        
        # Navigation buttons
        nav_items = [
            ("Dashboard", "🏠"),
            ("Authentication", "🔐"),
            ("Manifest Manager", "📦"),
            ("License Manager", "📋"),
            ("System Logs", "📝"),
            ("Settings", "⚙️"),
            ("Admin Tools", "🔧")
        ]
        
        for title, icon in nav_items:
            btn = ctk.CTkButton(
                nav_frame,
                text=f"{icon} {title}",
                width=180,
                height=40,
                anchor="w",
                command=lambda t=title: self.show_advanced_view(t)
            )
            btn.pack(pady=5, padx=10)
        
        # Content area
        self.advanced_content_frame = ctk.CTkFrame(content_container, corner_radius=15)
        self.advanced_content_frame.pack(side="right", fill="both", expand=True)
        
        # Show dashboard by default
        self.show_advanced_view("Dashboard")
    
    def show_advanced_view(self, view_name):
        """Show different advanced views"""
        # Clear content frame
        for widget in self.advanced_content_frame.winfo_children():
            widget.destroy()
        
        # Create view based on selection
        view_label = ctk.CTkLabel(
            self.advanced_content_frame,
            text=f"{view_name} - Coming Soon",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        view_label.pack(expand=True)
        
        # Add some basic functionality for demonstration
        if view_name == "Dashboard":
            self.create_dashboard_content()
    
    def create_dashboard_content(self):
        """Create dashboard content for advanced mode"""
        # Clear and create dashboard
        for widget in self.advanced_content_frame.winfo_children():
            widget.destroy()
        
        # Dashboard title
        title = ctk.CTkLabel(
            self.advanced_content_frame,
            text="🏠 Dashboard",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=20)
        
        # Status cards
        cards_frame = ctk.CTkFrame(self.advanced_content_frame)
        cards_frame.pack(fill="x", padx=20, pady=10)
        
        # Authentication status
        auth_card = ctk.CTkFrame(cards_frame)
        auth_card.pack(side="left", fill="both", expand=True, padx=5)
        
        auth_status = "✅ Authenticated" if self.authenticated else "❌ Not Authenticated"
        ctk.CTkLabel(auth_card, text="Authentication", font=ctk.CTkFont(weight="bold")).pack(pady=5)
        ctk.CTkLabel(auth_card, text=auth_status).pack(pady=5)
        
        # Steam status
        steam_card = ctk.CTkFrame(cards_frame)
        steam_card.pack(side="left", fill="both", expand=True, padx=5)
        
        steam_status = "✅ Detected" if self.steam_path else "❌ Not Found"
        ctk.CTkLabel(steam_card, text="Steam", font=ctk.CTkFont(weight="bold")).pack(pady=5)
        ctk.CTkLabel(steam_card, text=steam_status).pack(pady=5)
    
    def close_advanced_mode(self, event=None):
        """Close advanced mode and return to simple interface"""
        self.advanced_mode = False
        self.root.geometry("600x500")
        self.center_window()
        self.create_license_interface()
    
    def _post_auth_setup(self):
        """Run post-authentication setup tasks"""
        try:
            if STEAM_PATH:
                remove_lock(LOCK_FILE)
                clearCache()
                checkKoaLoader(self.keyauthapp)
                checkGreenLuma(self.keyauthapp, DATABASE_FILE, self.encryption_key)
                checkMiniTool(self.keyauthapp)
            self.log_message("[MTYB] Setup complete")
        except Exception as e:
            self.log_message(f"[MTYB] Setup error: {str(e)}")
    
    def initialize_app(self):
        """Initialize application"""
        try:
            if STEAM_PATH:
                addDefenderExclusion(STEAM_PATH)
            checkDatabase(DATABASE_FILE)
            self.log_message("[MTYB] Application initialized successfully")
        except Exception as e:
            self.log_message(f"[MTYB] Initialization error: {str(e)}")
    
    def log_message(self, message):
        """Log message to console"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()


def main_gui():
    """Main entry point for the simple GUI"""
    app = SimpleSteamManifestGUI()
    app.run()


if __name__ == '__main__':
    main_gui()
